import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { Auth } from '../../core/services/auth';

@Component({
  selector: 'app-unauthorized',
  standalone: false,
  template: `
    <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
          <mat-icon class="mx-auto h-24 w-24 text-red-500 text-6xl">block</mat-icon>
          <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
            Access Denied
          </h2>
          <p class="mt-2 text-sm text-gray-600">
            You don't have permission to access this resource.
          </p>
        </div>
      </div>

      <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div class="text-center">
            <p class="text-gray-700 mb-4">
              Your current role: <span class="font-semibold text-primary-600">{{ userRole }}</span>
            </p>
            <p class="text-sm text-gray-500 mb-6">
              If you believe this is an error, please contact your system administrator.
            </p>
            
            <div class="space-y-3">
              <button 
                mat-raised-button 
                color="primary" 
                (click)="goToDashboard()"
                class="w-full"
              >
                Go to Dashboard
              </button>
              
              <button 
                mat-button 
                (click)="goBack()"
                class="w-full"
              >
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .text-6xl {
      font-size: 4rem;
    }
  `]
})
export class UnauthorizedComponent {
  userRole: string | null;

  constructor(
    private router: Router,
    private authService: Auth
  ) {
    this.userRole = this.authService.getUserRole();
  }

  goToDashboard(): void {
    this.router.navigate(['/dashboard']);
  }

  goBack(): void {
    window.history.back();
  }
}
