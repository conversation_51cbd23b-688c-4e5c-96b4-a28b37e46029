import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SharedModule } from '../shared/shared-module';
import { PatientsRoutingModule } from './patients-routing.module';
import { PatientList } from './patient-list/patient-list';
import { PatientDetail } from './patient-detail/patient-detail';
import { PatientForm } from './patient-form/patient-form';

@NgModule({
  declarations: [
    PatientList,
    PatientDetail,
    PatientForm
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PatientsRoutingModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatTableModule,
    MatProgressSpinnerModule,
    SharedModule
  ]
})
export class PatientsModule { }
