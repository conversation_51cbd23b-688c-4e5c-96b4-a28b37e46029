import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PatientList } from './patient-list/patient-list';
import { PatientDetail } from './patient-detail/patient-detail';
import { PatientForm } from './patient-form/patient-form';
import { RoleGuard } from '../core/guards/role.guard';

const routes: Routes = [
  { 
    path: '', 
    component: PatientList,
    canActivate: [RoleGuard],
    data: { permissions: ['view_patients', 'manage_patients'] }
  },
  { 
    path: 'new', 
    component: PatientForm,
    canActivate: [RoleGuard],
    data: { permissions: ['manage_patients'] }
  },
  { 
    path: ':id', 
    component: PatientDetail,
    canActivate: [RoleGuard],
    data: { permissions: ['view_patients', 'manage_patients'] }
  },
  { 
    path: ':id/edit', 
    component: PatientForm,
    canActivate: [RoleGuard],
    data: { permissions: ['manage_patients'] }
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PatientsRoutingModule { }
