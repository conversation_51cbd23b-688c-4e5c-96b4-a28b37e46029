const express = require('express');
const Patient = require('../models/Patient');
const { authenticateToken, requirePermissions, authorizeRoles } = require('../middlewares/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all patients (Admin and Doctors only)
router.get('/', requirePermissions('view_patients', 'manage_patients'), async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const query = search ? {
      $or: [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { patientId: { $regex: search, $options: 'i' } }
      ]
    } : {};

    const patients = await Patient.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Patient.countDocuments(query);

    res.json({
      patients,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get patient by ID (Admin, Doctors, or the patient themselves)
router.get('/:id', async (req, res) => {
  try {
    // Check if patient is accessing their own data
    if (req.user.role === 'Patient') {
      // For patients, find their patient record by user ID
      const patient = await Patient.findOne({ _id: req.user._id });
      if (!patient) {
        return res.status(404).json({ message: 'Patient profile not found' });
      }
      return res.json(patient);
    }

    // Check permissions for non-patients
    if (req.user.role !== 'Patient' && !req.user.hasAnyPermission(['view_patients', 'manage_patients'])) {
      return res.status(403).json({ message: 'Access denied. Insufficient permissions.' });
    }

    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    res.json(patient);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Create new patient (Admin and Doctors only)
router.post('/', requirePermissions('manage_patients'), async (req, res) => {
  try {
    const patient = new Patient(req.body);
    await patient.save();
    res.status(201).json({
      message: 'Patient created successfully',
      patient
    });
  } catch (error) {
    if (error.code === 11000) {
      res.status(400).json({ message: 'Patient with this email already exists' });
    } else {
      res.status(400).json({ message: error.message });
    }
  }
});

// Update patient (Admin, Doctors, or patient themselves)
router.put('/:id', async (req, res) => {
  try {
    // Check if patient is updating their own data
    if (req.user.role === 'Patient') {
      if (req.user._id.toString() !== req.params.id) {
        return res.status(403).json({ message: 'Access denied. You can only update your own profile.' });
      }
      // Patients can only update certain fields
      const allowedFields = ['phone', 'address', 'emergencyContact', 'allergies'];
      const updateData = {};
      allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
          updateData[field] = req.body[field];
        }
      });
      req.body = updateData;
    } else {
      // Check permissions for non-patients
      if (!req.user.hasAnyPermission(['manage_patients'])) {
        return res.status(403).json({ message: 'Access denied. Insufficient permissions.' });
      }
    }

    const patient = await Patient.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    res.json({
      message: 'Patient updated successfully',
      patient
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Delete patient (soft delete) - Admin only
router.delete('/:id', authorizeRoles('Admin'), async (req, res) => {
  try {
    const patient = await Patient.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    res.json({ message: 'Patient deactivated successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get patient's medical history (Admin, Doctors, or patient themselves)
router.get('/:id/medical-history', async (req, res) => {
  try {
    // Check access permissions
    if (req.user.role === 'Patient' && req.user._id.toString() !== req.params.id) {
      return res.status(403).json({ message: 'Access denied. You can only view your own medical history.' });
    }

    if (req.user.role !== 'Patient' && !req.user.hasAnyPermission(['view_patients', 'view_medical_history'])) {
      return res.status(403).json({ message: 'Access denied. Insufficient permissions.' });
    }

    const patient = await Patient.findById(req.params.id).select('medicalHistory');
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    res.json(patient.medicalHistory);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Add medical history entry (Doctors and Admin only)
router.post('/:id/medical-history', requirePermissions('view_medical_history', 'manage_patients'), async (req, res) => {
  try {
    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    patient.medicalHistory.push(req.body);
    await patient.save();

    res.status(201).json({
      message: 'Medical history added successfully',
      medicalHistory: patient.medicalHistory
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
