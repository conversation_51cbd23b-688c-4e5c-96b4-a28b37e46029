import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DoctorList } from './doctor-list/doctor-list';
import { DoctorDeta<PERSON> } from './doctor-detail/doctor-detail';
import { RoleGuard } from '../core/guards/role.guard';

const routes: Routes = [
  {
    path: '',
    component: DoctorList
  },
  {
    path: ':id',
    component: DoctorDeta<PERSON>
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DoctorsRoutingModule { }
