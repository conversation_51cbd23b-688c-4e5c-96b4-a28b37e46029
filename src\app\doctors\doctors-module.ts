import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { DoctorList } from './doctor-list/doctor-list';
import { Doctor<PERSON><PERSON><PERSON> } from './doctor-detail/doctor-detail';

const routes: Routes = [
  { path: '', component: Doctor<PERSON><PERSON> },
  { path: ':id', component: Doctor<PERSON><PERSON><PERSON> }
];

@NgModule({
  declarations: [
    Doctor<PERSON><PERSON>,
    DoctorDeta<PERSON>
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ]
})
export class DoctorsModule { }
