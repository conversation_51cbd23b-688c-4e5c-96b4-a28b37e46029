const express = require('express');
const Report = require('../models/Report');
const { authenticateToken, requirePermissions, authorizeRoles } = require('../middlewares/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all reports (<PERSON><PERSON> and Doctor can see all, Patient only their own)
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status = '', reportType = '', patientId = '' } = req.query;
    
    let query = {};
    
    // Role-based filtering
    if (req.user.role === 'Patient') {
      query.patient = req.user._id;
    } else if (req.user.role === 'Doctor') {
      // Doctors can see reports they created or are assigned to review
      query.$or = [
        { doctor: req.user._id },
        { reviewedBy: req.user._id }
      ];
    }
    // Admin can see all reports (no additional filtering)
    
    if (status) query.status = status;
    if (reportType) query.reportType = reportType;
    if (patientId && req.user.role !== 'Patient') query.patient = patientId;

    const reports = await Report.find(query)
      .populate('patient', 'firstName lastName patientId email')
      .populate('doctor', 'firstName lastName doctorId specialization')
      .populate('reviewedBy', 'firstName lastName doctorId')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ reportDate: -1 });

    const total = await Report.countDocuments(query);

    res.json({
      reports,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get report by ID
router.get('/:id', async (req, res) => {
  try {
    let query = { _id: req.params.id };
    
    // Role-based access control
    if (req.user.role === 'Patient') {
      query.patient = req.user._id;
    } else if (req.user.role === 'Doctor') {
      query.$or = [
        { doctor: req.user._id },
        { reviewedBy: req.user._id }
      ];
    }
    
    const report = await Report.findOne(query)
      .populate('patient', 'firstName lastName patientId email phone')
      .populate('doctor', 'firstName lastName doctorId specialization')
      .populate('appointment', 'appointmentId appointmentDate')
      .populate('reviewedBy', 'firstName lastName doctorId');
      
    if (!report) {
      return res.status(404).json({ message: 'Report not found or access denied' });
    }
    
    res.json(report);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Create new report (Only Doctors and Admin)
router.post('/', requirePermissions('create_reports', 'manage_reports'), async (req, res) => {
  try {
    const reportData = {
      ...req.body,
      doctor: req.user.role === 'Doctor' ? req.user._id : req.body.doctor
    };
    
    const report = new Report(reportData);
    await report.save();
    
    await report.populate('patient', 'firstName lastName patientId');
    await report.populate('doctor', 'firstName lastName doctorId specialization');
    
    res.status(201).json({
      message: 'Report created successfully',
      report
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Update report (Only the doctor who created it or Admin)
router.put('/:id', async (req, res) => {
  try {
    let query = { _id: req.params.id };
    
    // Only the creating doctor or admin can update
    if (req.user.role === 'Doctor') {
      query.doctor = req.user._id;
    }
    
    const report = await Report.findOneAndUpdate(
      query,
      req.body,
      { new: true, runValidators: true }
    ).populate('patient', 'firstName lastName patientId')
     .populate('doctor', 'firstName lastName doctorId specialization');
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found or access denied' });
    }
    
    res.json({
      message: 'Report updated successfully',
      report
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Review report (Only Doctors and Admin)
router.patch('/:id/review', requirePermissions('create_reports', 'manage_reports'), async (req, res) => {
  try {
    const { status, notes } = req.body;
    
    const report = await Report.findByIdAndUpdate(
      req.params.id,
      { 
        status,
        notes,
        reviewedBy: req.user._id,
        reviewedAt: new Date()
      },
      { new: true }
    ).populate('patient', 'firstName lastName patientId')
     .populate('doctor', 'firstName lastName doctorId specialization')
     .populate('reviewedBy', 'firstName lastName doctorId');
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    res.json({
      message: 'Report reviewed successfully',
      report
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Delete report (Only Admin)
router.delete('/:id', authorizeRoles('Admin'), async (req, res) => {
  try {
    const report = await Report.findByIdAndDelete(req.params.id);
    
    if (!report) {
      return res.status(404).json({ message: 'Report not found' });
    }
    
    res.json({ message: 'Report deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get reports by patient (Doctors and Admin only)
router.get('/patient/:patientId', requirePermissions('view_patients', 'manage_patients'), async (req, res) => {
  try {
    const reports = await Report.find({ patient: req.params.patientId })
      .populate('doctor', 'firstName lastName doctorId specialization')
      .populate('reviewedBy', 'firstName lastName doctorId')
      .sort({ reportDate: -1 });
    
    res.json(reports);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
