import { NgModule } from '@angular/core';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { SharedModule } from '../shared/shared-module';
import { Header } from './header/header';
import { Footer } from './footer/footer';
import { Sidebar } from './sidebar/sidebar';
import { AuthInterceptor } from './interceptors/auth.interceptor';

@NgModule({
  declarations: [
    Header,
    Footer,
    Sidebar
  ],
  imports: [
    SharedModule
  ],
  exports: [
    Header,
    Footer,
    Sidebar
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    }
  ]
})
export class CoreModule { }
